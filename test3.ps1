<#  --------------------  EDIT THESE FOUR VALUES  -------------------- #>

$org = 'PolyEnhancedOrg'    # ← owner of the *source* project   (organisation login)
$old = 1                    # ← project *number* in that org   (…/projects/1)
$me  = 'JortsEnjoyer0'      # ← owner of the *destination*     (your user login)
$new = 8                    # ← project *number* in your acct  (…/projects/8)

<#  ------------------------------------------------------------------ #>


# ---------------------------------------------------------------------
# Helper: resolve a Project V2 node-ID with a one-shot GraphQL query
# ---------------------------------------------------------------------
function Get-ProjectId {
    param(
        [string]$Owner,      # org or user login
        [int]   $Number,     # project number
        [ValidateSet('organization','user')]
        [string]$Kind        # which GraphQL branch to query
    )

    $query = @"
query {
  $Kind(login: \"$Owner\") {
    projectV2(number: $Number) { id }
  }
}
"@

    gh api graphql -f query="$query" --jq '..|.id? // empty'
}

$srcId = Get-ProjectId -Owner $org -Number $old -Kind organization
$dstId = Get-ProjectId -Owner $me  -Number $new -Kind user

if (-not $srcId -or -not $dstId) {
    Write-Error "Couldn’t resolve one of the project IDs. Check the logins + numbers."; exit 1
}

# ---------------------------------------------------------------------
# 1. Pull every card + Status & Size from the SOURCE project
# ---------------------------------------------------------------------
$itemsQuery = @'
query ($pid:ID!, $first:Int!) {
  node(id:$pid) {
    ... on ProjectV2 {
      items(first:$first) {
        nodes {
          id
          content { ... on UniformResourceLocatable { url } }
          fieldValues(first:20) {
            nodes {
              ... on ProjectV2ItemFieldSingleSelectValue {
                field { ... on ProjectV2FieldCommon { id name } }
                name
              }
            }
          }
        }
      }
    }
  }
}
'@

$srcItems = gh api graphql `
            -f query=$itemsQuery -F pid=$srcId -F first=1000 `
            --jq '.data.node.items.nodes' | ConvertFrom-Json
Write-Host "Source items fetched: $($srcItems.Count)"

# ---------------------------------------------------------------------
# 2. Pull single-select fields & options from the DEST project
# ---------------------------------------------------------------------
$fieldsQuery = @'
query ($pid:ID!) {
  node(id:$pid) {
    ... on ProjectV2 {
      fields(first:50) {
        nodes {
          ... on ProjectV2SingleSelectField {
            id
            name
            options { id name }
          }
        }
      }
    }
  }
}
'@

$dstFields = gh api graphql -f query=$fieldsQuery -F pid=$dstId `
             --jq '.data.node.fields.nodes' | ConvertFrom-Json

# build look-ups
$fieldIdByName = @{}
$optIdByName   = @{}
foreach ($f in $dstFields) {
    $fieldIdByName[$f.name] = $f.id
    $map = @{}
    foreach ($o in $f.options) { $map[$o.name] = $o.id }
    $optIdByName[$f.name] = $map
}

# ---------------------------------------------------------------------
# 3. Build {URL → item-ID} map for DESTINATION project
# ---------------------------------------------------------------------
$dstJson = gh project item-list $new --owner $me --format json --limit 1000 |
           ConvertFrom-Json
$dstMap = @{}
foreach ($d in $dstJson.items) {
    if ($d.content -and $d.content.url) { $dstMap[$d.content.url] = $d.id }
}
Write-Host "Destination items indexed: $($dstMap.Count)"

# ---------------------------------------------------------------------
# 4. Update Status & Size on each copied card
# ---------------------------------------------------------------------
$mutation = @'
mutation ($proj:ID!, $item:ID!, $field:ID!, $opt:ID!) {
  updateProjectV2ItemFieldValue(
    input:{
      projectId:$proj
      itemId:$item
      fieldId:$field
      value:{ singleSelectOptionId:$opt }
    }) { clientMutationId }
}
'@

$updated = 0
foreach ($src in $srcItems) {
    if (-not $src.content -or -not $src.content.url) { continue }
    $url = $src.content.url
    if (-not $dstMap.ContainsKey($url)) { continue }
    $dstItemId = $dstMap[$url]

    foreach ($fv in $src.fieldValues) {
        $fname = $fv.field.name
        if ($fname -notin @('Status','Size')) { continue }

        $optName = $fv.name
        $fieldId = $fieldIdByName[$fname]
        $optId   = $optIdByName[$fname][$optName]

        if (-not $fieldId -or -not $optId) { continue }

        gh api graphql -f query=$mutation `
          -F proj=$dstId -F item=$dstItemId -F field=$fieldId -F opt=$optId `
          1>$null
        $updated++
    }
}

Write-Host "`nField values updated: $updated"
Write-Host "Open https://github.com/users/$me/projects/$new to verify."
