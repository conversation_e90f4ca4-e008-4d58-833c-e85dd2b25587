# ---------- EDIT THESE FOUR VALUES ------------------------------------
$org = 'PolyEnhancedOrg'   # organisation that owns the SOURCE project
$old = 1                   # project NUMBER inside that org
$me  = 'JortsEnjoyer0'     # your personal account login
$new = 8                   # project NUMBER in your account
# ----------------------------------------------------------------------

Write-Host "`n=== 1. Verify SOURCE project ============================="
gh project view $old --owner $org

Write-Host "`n=== 2. Fetch items ======================================="
$raw   = gh project item-list $old --owner $org --limit 1000 --format json | ConvertFrom-Json
$items = $raw.items
Write-Host "Total items fetched: $($items.Count)"

$withUrls = $items | Where-Object { $_.content -and $_.content.url }
Write-Host "Items that have a URL (will be copied): $($withUrls.Count)`n"

# List the URLs so you can eyeball them
$withUrls | ForEach-Object { Write-Host $_.content.url }

if (!$withUrls) {
    Write-Warning "No items have content.url (did you point at the right project?)"
    exit 1
}

Write-Host "`n=== 3. Copy items to destination project ================="
foreach ($itm in $withUrls) {
    $url = $itm.content.url
    gh project item-add $new --owner $me --url $url 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Warning "⚠️  failed to add $url (already present or other error)"
    }
}

Write-Host "`n=== 4. Destination project summary ======================="
gh project view $new --owner $me
