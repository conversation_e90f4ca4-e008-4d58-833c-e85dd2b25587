# ---------- EDIT THESE FOUR VALUES ------------------------------------
$org = 'PolyEnhancedOrg'   # organisation that owns the source project
$old = 1                   # project NUMBER inside that org
$me  = 'JortsEnjoyer0'     # your personal account login
$new = 8                   # project NUMBER in your personal account
# ----------------------------------------------------------------------

Write-Host "`n=== 1. Verify SOURCE project ($org/projects/$old) =========="
gh project view $old --owner $org

Write-Host "`n=== 2. Fetch items ========================================="
$items = gh project item-list $old --owner $org --limit 1000 --format json |
         ConvertFrom-Json

Write-Host "Total items fetched: $($items.Count)"

$itemsWithUrls = $items | Where-Object { $_.content -and $_.content.url }
Write-Host "Items that have a URL (will be copied): $($itemsWithUrls.Count)`n"

# List the URLs for inspection
$itemsWithUrls | ForEach-Object { Write-Host $_.content.url }

if ($itemsWithUrls.Count -eq 0) {
    Write-Warning "No items contain a content.url. Either you pointed at the wrong project, or the project only contains draft cards."
    exit 1
}

Write-Host "`n=== 3. Copy items to destination project ($me/projects/$new) ==="
$itemsWithUrls | ForEach-Object {
    $url = $_.content.url
    gh project item-add $new --owner $me --url $url 2>$null
    if ($LASTEXITCODE -ne 0) {
        Write-Warning "Failed to add $url (already present or other error)"
    }
}

Write-Host "`n=== 4. Destination project summary ============================"
gh project view $new --owner $me
